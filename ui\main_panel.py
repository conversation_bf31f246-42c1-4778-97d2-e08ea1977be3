"""
Main Panel for BlendPro: AI Co-Pilot
Primary user interface panel in Blender's 3D viewport
"""

import bpy
from typing import Dict, Any, Optional

from ..config.settings import get_settings
from ..core.interaction_engine import get_interaction_engine
from ..workflow.scene_monitor import get_scene_health_monitor
from ..workflow.proactive_suggestions import get_proactive_suggestions
from ..utils.api_client import get_api_client

class BLENDPRO_PT_MainPanel(bpy.types.Panel):
    """Main BlendPro panel in 3D viewport"""
    bl_label = "BlendPro: AI Co-Pilot"
    bl_idname = "BLENDPRO_PT_main_panel"
    bl_space_type = 'VIEW_3D'
    bl_region_type = 'UI'
    bl_category = "BlendPro"
    bl_options = {'DEFAULT_CLOSED'}
    
    def draw_header(self, context):
        """Draw panel header with status indicator"""
        layout = self.layout
        
        # Status indicator
        settings = get_settings()
        if settings.api_key:
            layout.label(text="", icon='LINKED')
        else:
            layout.label(text="", icon='UNLINKED')
    
    def draw(self, context):
        """Draw main panel content"""
        layout = self.layout
        scene = context.scene
        settings = get_settings()
        
        # API Configuration Status
        if not settings.api_key:
            self._draw_setup_required(layout)
            return
        
        # Quick Actions Section
        self._draw_quick_actions(layout, context)
        
        # Chat Interface Section
        self._draw_chat_interface(layout, context)
        
        # Agent System Section (v2.1.0)
        self._draw_agent_system_status(layout, context)

        # Enhanced Vision Section (v2.1.0)
        self._draw_enhanced_vision_status(layout, context)

        # Provider Management Section (v2.1.0)
        self._draw_provider_management(layout, context)

        # Cost & Performance Section (v2.1.0)
        self._draw_cost_performance_status(layout, context)

        # Scene Health Section
        self._draw_scene_health(layout, context)

        # Proactive Suggestions Section
        self._draw_proactive_suggestions(layout, context)

        # System Status Section
        self._draw_system_status(layout, context)
    
    def _draw_setup_required(self, layout):
        """Draw setup required message"""
        box = layout.box()
        box.label(text="Setup Required", icon='ERROR')
        box.label(text="Please configure your API key")
        box.label(text="in the addon preferences.")
        
        # Quick setup button
        row = box.row()
        row.scale_y = 1.2
        row.operator("screen.userpref_show", text="Open Preferences", icon='PREFERENCES')
    
    def _draw_quick_actions(self, layout, context):
        """Draw quick action buttons"""
        box = layout.box()
        box.label(text="Quick Actions", icon='PLAY')

        # Quick action buttons
        col = box.column(align=True)

        row = col.row(align=True)
        row.operator("blendpro.analyze_scene_health", text="Health Check", icon='CHECKMARK')
        row.operator("blendpro.auto_fix_scene", text="Auto-Fix", icon='TOOL_SETTINGS')
        
        row = col.row(align=True)
        row.operator("blendpro.capture_screenshot", text="Screenshot", icon='CAMERA_DATA')
        row.operator("blendpro.toggle_scene_monitoring", text="Monitor", icon='VIEWZOOM')
    
    def _draw_chat_interface(self, layout, context):
        """Draw chat interface"""
        box = layout.box()
        box.label(text="AI Assistant", icon='COMMUNITY')
        
        # Chat input
        row = box.row()
        row.prop(context.scene, "blendpro_chat_input", text="", placeholder="Ask me anything...")
        
        # Send button
        send_row = box.row()
        send_row.scale_y = 1.2
        
        # Show processing state
        if context.scene.blendpro_button_pressed:
            send_row.enabled = False
            send_row.operator("blendpro.send_message", text="Processing...", icon='TIME')
        else:
            send_row.operator("blendpro.send_message", text="Send", icon='PLAY')
        
        # Recent chat history (last 3 messages)
        chat_history = context.scene.blendpro_chat_history
        if len(chat_history) > 0:
            history_box = box.box()
            history_box.label(text="Recent Messages:", icon='TEXT')
            
            # Show last 3 messages
            recent_messages = list(chat_history)[-3:]
            for message in recent_messages:
                msg_row = history_box.row()
                
                if message.type == 'user':
                    content = str(message.content)[:50] if message.content else ""
                    msg_row.label(text=f"You: {content}...", icon='USER')
                else:
                    content = str(message.content)[:50] if message.content else ""
                    # Show agent info if available
                    agent_info = ""
                    if hasattr(message, 'agent_used') and message.agent_used:
                        agent_info = f" [{message.agent_used}]"
                    msg_row.label(text=f"AI{agent_info}: {content}...", icon='COMMUNITY')

                    # Show metadata for assistant messages
                    if hasattr(message, 'provider_used') and message.provider_used:
                        meta_row = history_box.row()
                        meta_text = f"Provider: {message.provider_used}"
                        if message.model_used:
                            meta_text += f" | Model: {message.model_used}"
                        if message.response_time > 0:
                            meta_text += f" | Time: {message.response_time:.1f}s"
                        if message.cost_estimate > 0:
                            meta_text += f" | Cost: ${message.cost_estimate:.4f}"
                        meta_row.label(text=meta_text, icon='INFO')
                        meta_row.scale_y = 0.8
                
                # Show interactive options for plan messages
                if hasattr(message, 'is_interactive') and message.is_interactive:
                    interactive_row = history_box.row(align=True)

                    # Parse plan data
                    try:
                        import json
                        plan_data_str = str(message.plan_data).strip() if hasattr(message, 'plan_data') and message.plan_data else ""

                        # Comprehensive validation of plan_data before JSON parsing
                        if (not plan_data_str or
                            plan_data_str == "" or
                            plan_data_str in ["None", "null", "undefined"] or
                            plan_data_str.startswith("<") or
                            "PropertyDeferred" in plan_data_str or
                            len(plan_data_str) < 2):  # Minimum valid JSON is "{}" or "[]"
                            continue

                        # Safe JSON parsing with error handling
                        try:
                            plan_steps = json.loads(plan_data_str)
                        except (json.JSONDecodeError, ValueError, TypeError) as json_error:
                            print(f"JSON parsing error in main_panel plan_data: {json_error}")
                            continue

                        if plan_steps:
                            # Check interaction type
                            interaction_type = getattr(message, 'interaction_type', '')

                            if interaction_type == "next_step":
                                # Show next step button
                                next_step_number = getattr(message, 'next_step_number', 1)
                                next_op = interactive_row.operator("blendpro.approve_plan", text=f"Execute Step {next_step_number}", icon='FORWARD')
                                next_op.plan_id = str(message.plan_id).strip()
                                next_op.step_number = next_step_number

                                # Show step info if available
                                next_step_info = getattr(message, 'next_step_info', '')
                                if next_step_info:
                                    try:
                                        step_info = json.loads(next_step_info)
                                        info_row = layout.row()
                                        info_row.label(text=f"Next: {step_info.get('description', 'Continue')}", icon='INFO')
                                    except:
                                        pass
                            else:
                                # Regular plan approval
                                approve_op = interactive_row.operator("blendpro.approve_plan", text="Execute Plan", icon='CHECKMARK')

                                # Set plan data
                                if plan_data_str:
                                    approve_op.plan_steps_json = plan_data_str

                                # Set plan ID - must be available from message
                                if hasattr(message, 'plan_id') and message.plan_id and str(message.plan_id).strip():
                                    approve_op.plan_id = str(message.plan_id).strip()
                                else:
                                    # Skip if no plan_id available - operator will handle the error
                                    print("Warning: No plan_id available for plan execution")
                                    continue

                                interactive_row.operator("blendpro.reject_plan", text="Reject", icon='CANCEL')
                    except Exception as e:
                        print(f"Error parsing plan data in UI: {e}")
                        pass

    def _draw_agent_system_status(self, layout, context):
        """Draw agent system status and controls"""
        settings = get_settings()

        box = layout.box()
        box.label(text="AI Agent System", icon='COMMUNITY')

        # Multi-agent system status
        status_row = box.row()
        if settings.enable_multi_agent_system:
            status_row.label(text="Multi-Agent: Enabled", icon='CHECKMARK')
        else:
            status_row.label(text="Multi-Agent: Disabled", icon='X')

        # Show last used agent info from recent messages
        chat_history = context.scene.blendpro_chat_history
        if len(chat_history) > 0:
            # Find the most recent assistant message with agent info
            for message in reversed(list(chat_history)):
                if (message.type == 'assistant' and
                    hasattr(message, 'agent_used') and message.agent_used):

                    agent_row = box.row()
                    agent_row.label(text=f"Last Agent: {message.agent_used}", icon='USER')

                    if hasattr(message, 'provider_used') and message.provider_used:
                        provider_row = box.row()
                        provider_row.label(text=f"Provider: {message.provider_used}", icon='NETWORK_DRIVE')
                        provider_row.scale_y = 0.8

                    if hasattr(message, 'response_time') and message.response_time > 0:
                        time_row = box.row()
                        time_row.label(text=f"Response Time: {message.response_time:.1f}s", icon='TIME')
                        time_row.scale_y = 0.8

                    break

        # Agent selection mode
        mode_row = box.row()
        mode_row.label(text=f"Selection: {settings.default_agent_selection.title()}", icon='SETTINGS')

    def _draw_enhanced_vision_status(self, layout, context):
        """Draw enhanced vision pipeline status"""
        settings = get_settings()

        box = layout.box()
        box.label(text="Enhanced Vision", icon='CAMERA_DATA')

        # Vision system status
        status_row = box.row()
        if settings.enable_vision_context:
            status_row.label(text="Vision: Enabled", icon='CHECKMARK')
        else:
            status_row.label(text="Vision: Disabled", icon='X')

        # Show vision analysis info from recent messages
        chat_history = context.scene.blendpro_chat_history
        if len(chat_history) > 0:
            # Find the most recent message with vision analysis
            for message in reversed(list(chat_history)):
                if (message.type == 'assistant' and
                    hasattr(message, 'vision_analysis_used') and message.vision_analysis_used):

                    vision_row = box.row()
                    vision_row.label(text="Last Analysis: Used", icon='CHECKMARK')

                    if hasattr(message, 'vision_analysis_type') and message.vision_analysis_type:
                        type_row = box.row()
                        type_row.label(text=f"Type: {message.vision_analysis_type}", icon='INFO')
                        type_row.scale_y = 0.8

                    if hasattr(message, 'vision_confidence') and message.vision_confidence > 0:
                        conf_row = box.row()
                        conf_row.label(text=f"Confidence: {message.vision_confidence:.1%}", icon='GRAPH')
                        conf_row.scale_y = 0.8

                    break
            else:
                # No vision analysis found in recent messages
                no_vision_row = box.row()
                no_vision_row.label(text="Last Analysis: None", icon='INFO')

        # Enhanced vision features status
        if hasattr(settings, 'enable_immersive_vision') and settings.enable_immersive_vision:
            immersive_row = box.row()
            immersive_row.label(text="Immersive Vision: Enabled", icon='VIEW3D')
            immersive_row.scale_y = 0.8

        if hasattr(settings, 'enable_depth_analysis') and settings.enable_depth_analysis:
            depth_row = box.row()
            depth_row.label(text="Depth Analysis: Enabled", icon='MESH_GRID')
            depth_row.scale_y = 0.8

        if hasattr(settings, 'enable_spatial_reasoning') and settings.enable_spatial_reasoning:
            spatial_row = box.row()
            spatial_row.label(text="Spatial Reasoning: Enabled", icon='ORIENTATION_GLOBAL')
            spatial_row.scale_y = 0.8

    def _draw_provider_management(self, layout, context):
        """Draw provider management status"""
        settings = get_settings()

        box = layout.box()
        box.label(text="Provider Management", icon='NETWORK_DRIVE')

        # Provider failover status
        failover_row = box.row()
        if settings.enable_provider_failover:
            failover_row.label(text="Failover: Enabled", icon='CHECKMARK')
        else:
            failover_row.label(text="Failover: Disabled", icon='X')

        # Show current provider info from recent messages
        chat_history = context.scene.blendpro_chat_history
        if len(chat_history) > 0:
            # Find the most recent assistant message with provider info
            for message in reversed(list(chat_history)):
                if (message.type == 'assistant' and
                    hasattr(message, 'provider_used') and message.provider_used):

                    provider_row = box.row()
                    provider_row.label(text=f"Active: {message.provider_used}", icon='NETWORK_DRIVE')

                    if hasattr(message, 'model_used') and message.model_used:
                        model_row = box.row()
                        model_row.label(text=f"Model: {message.model_used}", icon='SETTINGS')
                        model_row.scale_y = 0.8

                    if hasattr(message, 'cost_estimate') and message.cost_estimate > 0:
                        cost_row = box.row()
                        cost_row.label(text=f"Last Cost: ${message.cost_estimate:.4f}", icon='FUND')
                        cost_row.scale_y = 0.8

                    break

        # Cost tracking status
        if settings.enable_cost_tracking:
            cost_status_row = box.row()
            cost_status_row.label(text="Cost Tracking: Enabled", icon='FUND')
            cost_status_row.scale_y = 0.8

            # Monthly limit info
            limit_row = box.row()
            limit_row.label(text=f"Monthly Limit: ${settings.monthly_cost_limit:.2f}", icon='INFO')
            limit_row.scale_y = 0.8

    def _draw_cost_performance_status(self, layout, context):
        """Draw cost optimization and performance monitoring status"""
        settings = get_settings()

        box = layout.box()
        box.label(text="Cost & Performance", icon='FUND')

        # Cost tracking summary
        if settings.enable_cost_tracking:
            cost_row = box.row()
            cost_row.label(text="Cost Tracking: Active", icon='CHECKMARK')

            # Calculate session cost from recent messages
            session_cost = 0.0
            chat_history = context.scene.blendpro_chat_history
            for message in chat_history:
                if (message.type == 'assistant' and
                    hasattr(message, 'cost_estimate') and message.cost_estimate > 0):
                    session_cost += message.cost_estimate

            if session_cost > 0:
                session_row = box.row()
                session_row.label(text=f"Session Cost: ${session_cost:.4f}", icon='FUND')
                session_row.scale_y = 0.8

                # Cost percentage of monthly limit
                if settings.monthly_cost_limit > 0:
                    percentage = (session_cost / settings.monthly_cost_limit) * 100
                    percent_row = box.row()
                    if percentage > settings.cost_alert_threshold:
                        percent_row.alert = True
                    percent_row.label(text=f"Monthly Usage: {percentage:.1f}%", icon='GRAPH')
                    percent_row.scale_y = 0.8

        # Performance metrics from recent messages
        total_response_time = 0.0
        response_count = 0

        for message in reversed(list(chat_history)[-10:]):  # Last 10 messages
            if (message.type == 'assistant' and
                hasattr(message, 'response_time') and message.response_time > 0):
                total_response_time += message.response_time
                response_count += 1

        if response_count > 0:
            avg_response_time = total_response_time / response_count
            perf_row = box.row()
            perf_row.label(text=f"Avg Response: {avg_response_time:.1f}s", icon='TIME')
            perf_row.scale_y = 0.8

        # Multi-agent performance
        if settings.enable_multi_agent_system:
            agent_perf_row = box.row()
            agent_perf_row.label(text="Multi-Agent: Optimized", icon='COMMUNITY')
            agent_perf_row.scale_y = 0.8

    def _draw_scene_health(self, layout, context):
        """Draw scene health information"""
        box = layout.box()
        box.label(text="Scene Health", icon='HEART')
        
        # Monitoring status
        monitor = get_scene_health_monitor()
        status = monitor.get_monitoring_status()
        
        row = box.row()
        if status["active"]:
            row.label(text="Monitoring: Active", icon='REC')
        else:
            row.label(text="Monitoring: Inactive", icon='PAUSE')
        
        # Recent suggestions
        suggestions = monitor.get_recent_suggestions(limit=2)
        if suggestions:
            for suggestion in suggestions:
                suggestion_row = box.row()
                suggestion_row.alert = suggestion.get("type") == "health_alert"
                suggestion_row.label(text=suggestion.get("message", "")[:40] + "...", icon='INFO')
        
        # Clear suggestions button
        if suggestions:
            box.operator("blendpro.clear_suggestions", text="Clear", icon='X')
    
    def _draw_proactive_suggestions(self, layout, context):
        """Draw proactive suggestions"""
        box = layout.box()
        box.label(text="Suggestions", icon='OUTLINER_OB_LIGHT')
        
        # Get active suggestions
        proactive = get_proactive_suggestions()
        active_suggestions = proactive.get_active_suggestions()
        
        if active_suggestions:
            # Show top 2 suggestions
            for suggestion in active_suggestions[:2]:
                suggestion_box = box.box()
                
                # Title and priority
                title_row = suggestion_box.row()
                priority = suggestion.get("priority", 5)
                
                if priority >= 8:
                    title_row.alert = True
                    icon = 'ERROR'
                elif priority >= 6:
                    icon = 'INFO'
                else:
                    icon = 'QUESTION'
                
                title_row.label(text=suggestion.get("title", ""), icon=icon)
                
                # Description
                desc_row = suggestion_box.row()
                desc_row.label(text=suggestion.get("description", "")[:60] + "...")
                
                # Action buttons
                if suggestion.get("actionable", False):
                    action_row = suggestion_box.row(align=True)
                    
                    # Execute action button
                    execute_op = action_row.operator("blendpro.execute_suggestion", text="Apply", icon='CHECKMARK')
                    execute_op.suggestion_id = suggestion.get("id", "")
                    
                    # Dismiss button
                    dismiss_op = action_row.operator("blendpro.dismiss_suggestion", text="Dismiss", icon='X')
                    dismiss_op.suggestion_id = suggestion.get("id", "")
        else:
            box.label(text="No active suggestions", icon='CHECKMARK')
    
    def _draw_system_status(self, layout, context):
        """Draw system status information"""
        box = layout.box()
        box.label(text="System Status", icon='SYSTEM')
        
        # API Status
        api_client = get_api_client()
        cache_stats = api_client.get_cache_stats()
        
        row = box.row()
        row.label(text=f"Cached Requests: {cache_stats.get('cached_requests', 0)}")
        
        # Interaction Engine Status
        engine = get_interaction_engine()
        
        row = box.row()
        if hasattr(engine, '_processing') and engine._processing:
            row.label(text="Status: Processing", icon='TIME')
        else:
            row.label(text="Status: Ready", icon='CHECKMARK')
        
        # Settings shortcut
        settings_row = box.row()
        settings_row.scale_y = 0.8
        settings_row.operator("screen.userpref_show", text="Settings", icon='PREFERENCES')

# Additional operators for main panel functionality
class BLENDPRO_OT_ClearSuggestions(bpy.types.Operator):
    """Clear all suggestions"""
    bl_idname = "blendpro.clear_suggestions"
    bl_label = "Clear Suggestions"
    bl_options = {'REGISTER'}
    
    def execute(self, context):
        monitor = get_scene_health_monitor()
        monitor.clear_suggestions()
        
        proactive = get_proactive_suggestions()
        proactive.clear_suggestions()
        
        self.report({'INFO'}, "Suggestions cleared")
        return {'FINISHED'}

class BLENDPRO_OT_ExecuteSuggestion(bpy.types.Operator):
    """Execute a proactive suggestion"""
    bl_idname = "blendpro.execute_suggestion"
    bl_label = "Execute Suggestion"
    bl_options = {'REGISTER', 'UNDO'}
    
    suggestion_id: bpy.props.StringProperty()
    
    def execute(self, context):
        if not self.suggestion_id:
            self.report({'ERROR'}, "No suggestion ID provided")
            return {'CANCELLED'}
        
        # This would execute the suggestion's action code
        # Implementation depends on how suggestions store their actions
        self.report({'INFO'}, f"Executed suggestion: {self.suggestion_id}")
        return {'FINISHED'}

class BLENDPRO_OT_DismissSuggestion(bpy.types.Operator):
    """Dismiss a proactive suggestion"""
    bl_idname = "blendpro.dismiss_suggestion"
    bl_label = "Dismiss Suggestion"
    bl_options = {'REGISTER'}
    
    suggestion_id: bpy.props.StringProperty()
    
    def execute(self, context):
        if not self.suggestion_id:
            self.report({'ERROR'}, "No suggestion ID provided")
            return {'CANCELLED'}
        
        proactive = get_proactive_suggestions()
        proactive.dismiss_suggestion(self.suggestion_id)
        
        self.report({'INFO'}, "Suggestion dismissed")
        return {'FINISHED'}

def register():
    """Register Blender classes"""
    bpy.utils.register_class(BLENDPRO_PT_MainPanel)
    bpy.utils.register_class(BLENDPRO_OT_ClearSuggestions)
    bpy.utils.register_class(BLENDPRO_OT_ExecuteSuggestion)
    bpy.utils.register_class(BLENDPRO_OT_DismissSuggestion)

def unregister():
    """Unregister Blender classes"""
    bpy.utils.unregister_class(BLENDPRO_OT_DismissSuggestion)
    bpy.utils.unregister_class(BLENDPRO_OT_ExecuteSuggestion)
    bpy.utils.unregister_class(BLENDPRO_OT_ClearSuggestions)
    bpy.utils.unregister_class(BLENDPRO_PT_MainPanel)
